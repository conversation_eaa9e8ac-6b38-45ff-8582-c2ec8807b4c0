import React from "react";
import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Article } from "@prisma/client";

export default function ArticleForm({
  mode,
  defaultValues,
  onAction,
}: {
  onAction?: DataFormOnActionDef<Article>;
  mode: "create" | "update";
  defaultValues?: Partial<Article>;
}) {
  return (
    <DataForm<Article>
      fields={[
        {
          accessorKey: "title",
          label: "عنوان المقال",
          placeholder: "ادخل عنوان هذا المقال",
        },
        {
          accessorKey: "article",
          label: "المحتوى",
          fieldType: "textarea",
          placeholder: "اكتب محتوى المقال ...",
        },

        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف المقال لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description: "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },

        { accessorKey: "id", label: "", fieldType: "hidden" },
      ]}
      mode={mode}
      onAction={onAction}
      defaultValues={defaultValues}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/articles"
      cancelButtonLabel="إلغاء"
    />
  );
}
