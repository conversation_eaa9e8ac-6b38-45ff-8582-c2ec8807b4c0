@import "tailwindcss";
@import "tw-animate-css";

@theme {
  --background-image-primary-gradient-x: linear-gradient(to right, #4e0971, #aa2556);
  --background-image-primary-gradient-y: linear-gradient(to top, #4e0971, #aa2556);

  --color-background: #fafafa;
  --color-foreground: #3e384d;

  --color-primary: #aa2556;
  --color-primary-foreground: #fafafa;

  --color-secondary: #3e384d;
  --color-secondary-foreground: #fafafa;

  --color-muted: #676079;
  --color-muted-foreground: #fafafa;

  --color-destructive: #e40808;
  --color-destructive-foreground: #fafafa;

  --color-paid: #feb602;
  --color-paid-foreground: #121212;

  --color-border: #676079;
  --color-input: #3e384d;
  --color-ring: #aa2556;

  --radius-lg: 0.5rem;
  --radius-md: calc(0.5rem - 2px);
  --radius-sm: calc(0.5rem - 4px);
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: #aa2556;
  border-radius: 5px;
}
* {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #aa2556 transparent;
  text-rendering: geometricPrecision;
}

/* font-["Noto_Kufi_Arabic",Tajawal,sans-serif] */
@layer base {
  html {
    @apply bg-secondary font-[Noto_Kufi_Arabic];
  }
  body {
    @apply bg-background text-secondary;
  }
  * {
    @apply border-border;
  }
  svg,
  img {
    @apply select-none;
  }
}
