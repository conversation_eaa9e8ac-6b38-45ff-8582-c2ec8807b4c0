import React from "react";
import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { BlogPost } from "@prisma/client";

export default function BlogPostForm({
  mode,
  defaultValues,
  onAction,
}: {
  onAction?: DataFormOnActionDef<BlogPost>;
  mode: "create" | "update";
  defaultValues?: Partial<BlogPost>;
}) {
  return (
    <DataForm<BlogPost>
      fields={[
        {
          accessorKey: "title",
          label: "عنوان المنشور",
          placeholder: "ادخل عنوان هذا المنشور",
        },
        {
          accessorKey: "content",
          label: "محتوى المنشور",
          fieldType: "textarea",
          placeholder: "اكتب محتوى المنشور ...",
        },
        {
          accessorKey: "image",
          label: "صورة المنشور",
          fieldType: "fileUploader",
          placeholder: "اختر صورة المنشور",
        },
        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف المنشور لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description: "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },

        { accessorKey: "id", label: "", fieldType: "hidden" },
      ]}
      mode={mode}
      onAction={onAction}
      defaultValues={defaultValues}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/blogPosts"
      cancelButtonLabel="إلغاء"
    />
  );
}
