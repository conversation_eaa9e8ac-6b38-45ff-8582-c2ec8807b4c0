import { z } from "zod";
import { DataFormFieldFileUploaderProps } from "../fileUploader/FileUploader";

export type DataFormPropsDef<TData extends { [K: string]: any }> = {
  mode: "create" | "update";
  callbackUrl?: string;
  actionButtonLabel?: string;
  cancelButtonLabel?: string;
  actionAndStartOverButtonLabel?: string;
  defaultValues?: { [K in keyof TData]?: TData[K] | any };

  fields: DataFormFiledsDef<TData>[];
  zodSchema?: z.ZodSchema<Partial<any>>;
  onAction?: DataFormOnActionDef<TData>;
};

export type DataFormFiledsDef<TData extends { [K: string]: any }> = {
  [K in keyof TData]: {
    label: string;
    accessorKey: K & string;
    required?: boolean;
    placeholder?: string;
    description?: string;
    fieldType?:
      | "input"
      | "textarea"
      | "select"
      | "inputArray"
      | "checkboxArray"
      | "fileUploader"
      | "hidden"
      | "checkbox";

    inputConfig?: DataFormInputConfig;
    textareaConfig?: DataFormTextareaConfig;
    selectConfig?: DataFormSelectConfig<TData[K]>;
    checkboxArrayConfig?: DataFormCheckboxArrayConfig<TData[K]>;
    inputArrayConfig?: DataFormInputArrayConfig;
    fileUploaderConfig?: DataFormFileUploaderConfig;
  };
}[keyof TData];

export type DataFormInputConfig = {
  validateWarning?: z.ZodType;
  type?: Exclude<React.HTMLInputTypeAttribute, "file">;
  autoComplete?: React.HTMLInputAutoCompleteAttribute;
};

export type DataFormTextareaConfig = {
  validateWarning?: z.ZodType;
};

export type DataFormSelectConfig<TValue> = {
  options: {
    label: string;
    value: TValue;
    icon?: React.JSX.Element;
  }[];
};

export type DataFormCheckboxArrayConfig<TValue> = {
  enableSearchInput?: boolean;
  searchInputPlaceholder?: string;
  options: {
    label: string;
    value: TValue[any];
    icon?: React.JSX.Element;
  }[];
};

export type DataFormInputArrayConfig = {
  validateWarning?: z.ZodType;
};

export type DataFormFileUploaderConfig = Omit<
  DataFormFieldFileUploaderProps,
  "defaultValue" | "disabled" | "required" | "placeholder" | "name"
>;

export type DataFormOnActionDef<TData extends { [K: string]: any }> = (params: {
  data: TData;
}) => Promise<{
  success: boolean;
  errors?: { [K in keyof TData]?: string[] };
  message?: string;
}>;

export type FileType = {
  url: string;
  fileName?: string;
  type?: string;
  size?: number;
  createAt?: Date;
  metaData?: { [K: string]: any };
};
