import Image from "next/image";
import aboutMyPhoto from "@/../public/about-my-photo.png";
import aboutMyHeaderBackground from "@/../public/about-my-header-background.jpg";

import { DividerRedTop, DividerRedBottom } from "@/components/ui/dividers";
import { DividerShadow_X, DividerShadow_Y } from "@/components/ui/svg/icons";

export default function AboutMePage() {
  return (
    <div className="mb-28 pt-16">
      <HeaderPage />
      <div className="mt-16 space-y-16 px-4 md:px-6">
        <div>
          <DividerText label="نبذة عن د. ناهد باشطح" />
          <p className="mt-3 max-w-(--breakpoint-lg) whitespace-pre-line leading-relaxed">{`أنا ناهد باشطح، إعلامية سعودية شغوفة بعالم الصحافة والإعلام، حيث بدأت رحلتي في هذا المجال منذ أكثر من 16 عامًا في صحيفة "الجزيرة"، التي كانت مدرستي الأولى في العمل الصحفي. في عام 2001، انتقلت إلى العمل في صحيفتي "الشرق الأوسط" و"الوطن"، كما نشرت مقالاتي في مجلتي "المجلة" و"لها"، وهو ما أتاح لي فرصة أوسع للتفاعل مع القارئ العربي.
          
          لاحقًا، عملت في صحيفة "الرياض" لمدة سبع سنوات، ثم عدت مرة أخرى إلى "الجزيرة"، حيث واصلت الكتابة والتفاعل مع القضايا الإعلامية والثقافية التي تهمني. اليوم، أضع كامل تركيزي في تأسيس صحيفتي الإلكترونية الخاصة، والتي أطمح من خلالها إلى تقديم محتوى نوعي يعكس رؤيتي لمستقبل الإعلام.
          
          إلى جانب الصحافة، كانت لي تجارب أخرى، حيث قدمت محاضرات حول العلاج المعرفي لاضطرابات ما بعد الصدمة في المؤتمر السنوي للرابطة البريطانية للعلاجات النفسية السلوكية والمعرفية (BABCP) في سيري عام 2011، وهو مجال أؤمن بأهميته في دعم الأفراد نفسيًا ومجتمعيًا.
          
          أنا مؤمنة بأن الصحة النفسية لا تقل أهمية عن الصحة الجسدية، ولذلك وجّهت جزءًا من مسيرتي نحو دراسة العلاج الشعوري والمعرفي، حيث تعمّقت في فهم آليات التعامل مع الصدمات النفسية والتجارب العاطفية العميقة. كان لي شرف تقديم محاضرات حول العلاج المعرفي لاضطرابات ما بعد الصدمة في المؤتمر السنوي للرابطة البريطانية للعلاجات النفسية السلوكية والمعرفية (BABCP) في سيري عام 2011، وهي تجربة أكدت لي مدى حاجة الأفراد إلى التوجيه العاطفي والوعي بأدوات التفكير الإيجابي.

أرى أن المشاعر والأفكار مترابطة بشكل وثيق، وأن قدرتنا على فهم دوافعنا الداخلية والتصالح مع تجاربنا الماضية تساهم بشكل كبير في تحسين جودة حياتنا. لهذا السبب، أهتم بنشر الوعي حول أساليب العلاج الشعوري، وكيفية التعامل مع الضغوط النفسية بطريقة بنّاءة تتيح للفرد استعادة توازنه العاطفي والفكري.

أسعى دائمًا إلى المزج بين الإعلام والعلاج النفسي المعرفي، حيث أؤمن بأن للكلمة تأثيرًا عميقًا في تغيير القناعات وتعزيز الوعي الذاتي، وأن الإعلام يمكن أن يكون أداة قوية في نشر ثقافة التعافي النفسي وتوفير المساحة للحوار العاطفي العميق.`}</p>
        </div>

        <div>
          <DividerText label="هــدفــي" />
          <ul className="mt-3 max-w-(--breakpoint-lg) list-outside list-disc space-y-3 pr-6">
            <li>
              <strong className="font-medium">نشر ثقافة الوعي العاطفي:</strong> أسعى إلى
              مساعدة الأفراد على فهم مشاعرهم والتعامل مع الصدمات بطرق صحية.
            </li>
            <li>
              <strong className="font-medium">تعزيز أساليب العلاج المعرفي:</strong> أهدف
              إلى دمج تقنيات العلاج الشعوري في حياتنا اليومية لمساعدتنا على التفكير بطريقة
              أكثر توازنًا وإيجابية.
            </li>
            <li>
              <strong className="font-medium">
                التواصل مع المجتمع من خلال الإعلام العلاجي:
              </strong>{" "}
              أطمح إلى استخدام الإعلام كوسيلة لنشر ثقافة التعافي النفسي، وتسليط الضوء على
              أهمية الصحة النفسية.
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}

function HeaderPage() {
  return (
    <div className="relative flex w-full flex-col overflow-clip bg-secondary px-3 py-16 lg:flex-row-reverse lg:items-center lg:justify-center lg:px-6">
      <div className="absolute -bottom-5 -right-32 size-72 rounded-full bg-primary-gradient-y opacity-50 blur-xs lg:-top-8 lg:bottom-auto lg:size-96"></div>

      <div className="absolute inset-0">
        <Image
          src={aboutMyHeaderBackground}
          alt=""
          className="h-full w-auto object-cover opacity-70 mix-blend-soft-light blur-xs lg:h-auto lg:w-full"
        />
      </div>
      <DividerRedTop />
      {/* النصوص */}
      <div className="relative flex h-24 items-center justify-center rounded-xl bg-background/[0.01] py-28 shadow-[0_-25px_20px_#00000040] backdrop-blur-md lg:w-[45%] lg:shadow-[0px_0_30px_#00000030]">
        <h1 className="text-nowrap text-center text-3xl text-background drop-shadow-[0_4px_2px_#00000050] sm:text-4xl">
          مـــن هـــي الدكتورة
          <span className="mt-4 block font-bold sm:mt-8 sm:text-4xl">
            نـــاهد باشطـــح؟
          </span>
        </h1>
      </div>

      {/* الضل الفاصل */}
      <DividerShadow_X className="w-full scale-x-110 scale-y-125 lg:hidden" />
      <DividerShadow_Y className="mx-4 hidden h-[410px] w-auto -scale-y-110 opacity-70 mix-blend-overlay lg:block" />

      {/* الصورة */}
      <div className="">
        <div className="mx-auto aspect-square w-full max-w-96 rounded-xl bg-background/[0.01] p-3 shadow-[0_25px_20px_#00000040] backdrop-blur-xl lg:w-96 lg:shadow-[60px_0_20px_#00000040]">
          <div className="relative h-full w-full overflow-clip rounded-sm bg-black/10 px-3 pt-3">
            <Image
              src={aboutMyPhoto}
              alt="ناهد باشطح"
              className="h-full w-auto object-cover mix-blend-overlay brightness-90 contrast-125 drop-shadow-[0_4px_7px_#00000050] grayscale-[20%]"
            />
          </div>
        </div>
      </div>
      <DividerRedBottom />
    </div>
  );
}

function DividerText({ label }: { label: string }) {
  return (
    <div className="flex w-full flex-nowrap items-center gap-4">
      <h2 id="lectures" className="scroll-mt-20 text-primary">
        {label}
      </h2>
      <hr className="mt-1.5 flex-1 border-primary" />
    </div>
  );
}
