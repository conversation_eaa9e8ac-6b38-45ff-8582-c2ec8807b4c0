import React from "react";
import DataFormFieldInput from "./DataFormFieldInput";
import DataFormFieldTextarea from "./DataFormFieldTextarea";

import { DataFormFiledsDef } from "./types";
import DataFormFieldSelect from "./DataFormFieldSelect";
import DataFormFieldInputArray from "./DataFormFieldInputArray";
import DataFormFieldCheckboxArray from "./DataFormFieldCheckboxArray";
import DataFormFieldFileUploader from "./DataFormFieldFileUploader";
import DataFormFieldCheckbox from "./DataFormFieldCheckbox";

export type DataFormFieldProps<TData extends { [K: string]: any }> = DataFormFiledsDef<TData> & {
  defaultValue: any;
  errorMessage?: string[];
  isPending: boolean;
};

export default function DataFormField<TData extends { [K: string]: any }>(
  props: DataFormFieldProps<TData>,
) {
  switch (props.fieldType) {
    case undefined:
      return <DataFormFieldInput {...props} />;

    case "input":
      return <DataFormFieldInput {...props} />;

    case "textarea":
      return <DataFormFieldTextarea {...props} />;

    case "select":
      return <DataFormFieldSelect {...props} />;

    case "inputArray":
      return <DataFormFieldInputArray {...props} />;

    case "checkboxArray":
      return <DataFormFieldCheckboxArray {...props} />;

    case "fileUploader":
      return <DataFormFieldFileUploader {...props} />;

    case "checkbox":
      return <DataFormFieldCheckbox {...props} />;

    case "hidden":
      return (
        <input defaultValue={props.defaultValue} type="hidden" name={props.accessorKey} />
      );
  }
}
