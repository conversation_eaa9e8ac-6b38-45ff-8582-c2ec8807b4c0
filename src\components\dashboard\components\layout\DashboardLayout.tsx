// import { cookies } from "next/headers";
import { AdminSessionProvider } from "../auth/useAdminSession";

import { SidebarProvider, SidebarTrigger } from "./navigation/Sidebar";
import { ThemeProvider } from "next-themes";
import { SidebarItemsType } from "./LayoutTypes";
import { AppSidebar } from "./navigation/AppSidebar";
import { Breadcrumbs } from "./navigation/Breadcrumbs";
import { Toaster } from "../ui/sonner";

// import { verifyAdminSession } from "../auth/admin-session";
// import { redirect } from "next/navigation";

type DashboardLayoutProps = {
  sidebarItems: SidebarItemsType;
  children: React.ReactNode;
  logo: React.ReactNode;
};

export default async function DashboardLayout({
  logo,
  children,
  sidebarItems,
}: DashboardLayoutProps) {
  // const adminSession = await verifyAdminSession();
  // const cookieStore = await cookies();
  // const sidebarDefaultOpen = cookieStore.get("sidebar:state")?.value === "true";
  // const sidebarOpenMenuId = cookieStore.get("sidebar:openMenuId")?.value;

  // if (!adminSession) redirect("/dashboard-login");

  return (
    <div
      dir="rtl"
      lang="ar"
      className="mx-auto max-w-(--breakpoint-2xl) overflow-x-clip bg-white antialiased dark:bg-gray-950"
    >
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <AdminSessionProvider>
          <SidebarProvider defaultOpen={undefined}>
            <AppSidebar openMenuId={undefined} sidebarItems={sidebarItems} logo={logo} />
            <div className="w-full">
              <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 border-b border-gray-200 bg-white px-4 dark:border-gray-800 dark:bg-gray-950">
                <SidebarTrigger className="-ml-1" />
                <div className="mr-2 h-4 w-px bg-gray-200 dark:bg-gray-800" />
                <Breadcrumbs sidebarItems={sidebarItems} />
              </header>
              <main className="@container">{children}</main>
            </div>
          </SidebarProvider>
        </AdminSessionProvider>
        {/* اذا كان هناك تذييل سيتم اضافته هنا */}
        <Toaster richColors closeButton dir="rtl" position="bottom-left" />
      </ThemeProvider>
    </div>
  );
}
