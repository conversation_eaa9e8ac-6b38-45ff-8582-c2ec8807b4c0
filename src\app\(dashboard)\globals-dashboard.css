@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --color-gray-925: #050814;

  /* للأسفل و الأعلى */
  --animate-slideDownAndFade: SlideDownAndFade 150ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideUpAndFade: SlideUpAndFade 150ms cubic-bezier(0.16, 1, 0.3, 1);

  /* لليمين والشمال */
  --animate-slideLeftAndFade: SlideLeftAndFade 150ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-slideRightAndFade: SlideRightAndFade 150ms cubic-bezier(0.16, 1, 0.3, 1);

  /* الدرايور */
  --animate-drawerSlideLeftAndFade: DrawerSlideLeftAndFade 300ms
    cubic-bezier(0.16, 1, 0.3, 1);
  --animate-drawerSlideRightAndFade: DrawerSlideRightAndFade 150ms;

  /* الديالوق */
  --animate-dialogOverlayShow: DialogOverlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-dialogContentShow: DialogContentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  --animate-hide: Hide 150ms cubic-bezier(0.16, 1, 0.3, 1);

  /* مفاتيح الحركة */

  @keyframes DialogOverlayShow {
    from: {
      opacity: 0;
    }
    to: {
      opacity: 1;
    }
  }
  @keyframes DialogContentShow {
    from: {
      opacity: 0;
      transform: translate(-50%, -45%) scale(0.95);
    }
    to: {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }
  @keyframes Hide {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }
  @keyframes DrawerSlideRightAndFade {
    from {
      opacity: 0;
      transform: translateX(100%);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes DrawerSlideLeftAndFade {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(100%);
    }
  }
  @keyframes SlideDownAndFade {
    from {
      opacity: 0;
      transform: translateY(-6px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes SlideLeftAndFade {
    from {
      opacity: 0;
      transform: translateX(6px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes SlideUpAndFade {
    from {
      opacity: 0;
      transform: translateY(6px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  @keyframes SlideRightAndFade {
    from {
      opacity: 0;
      transform: translateX(-6px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  @keyframes accordionOpen {
    from {
      height: 0px;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }
  @keyframes accordionClose {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0px;
    }
  }
}

@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    @apply border-gray-300 dark:border-gray-800;
  }
  html {
    @apply bg-gray-300 font-[Tajawal] dark:bg-gray-900;
  }
}

/* Custom scrollbar styling. Thanks @pranathiperii. */
::-webkit-scrollbar {
  width: 5px;
}
::-webkit-scrollbar-track {
  background: transparent;
}
::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 5px;
}
* {
  scrollbar-width: thin;
  scrollbar-color: #6b7280 transparent;
  text-rendering: optimizeLegibility;
}
table {
  text-rendering: optimizeSpeed !important;
}
