import Link from "next/link";
import FormatTime from "@/components/ui/format-time";

import { Pagination } from "@/components/ui/pagination";
import { getArticles } from "@/utils/get-data-from-db";
import { CalendarDays } from "lucide-react";
import { Article } from "@prisma/client";
import { Metadata } from "next";

type ArticlesProps = { params: Promise<{ pageIndex: string }> };

// ==================================================================================
// صفحة تعرض كل المقالات
// ==================================================================================
export async function generateMetadata(props: ArticlesProps): Promise<Metadata> {
  const pageIndex = Number((await props.params).pageIndex);
  const { data, pagination } = await getArticles({ pageIndex });
  const { currentPage, totalPages } = pagination;
  const titleArticles = data.flatMap((article) => article.title);

  // العلامات الوصفية للترقيم الصفحي
  const isPrevious = currentPage > 1;
  const isNext = totalPages > currentPage;
  const next = isNext ? `/media-experience/articles/${currentPage + 1}` : undefined;
  const previous = isPrevious ? `/media-experience/articles/${currentPage - 1}` : undefined;

  return {
    title: `مقالات د. ناهد باشطح [${pageIndex}]`,
    description: titleArticles.join(" || "),
    pagination: { previous, next },
    keywords: titleArticles,
  };
}

export default async function ArticlesPage(props: ArticlesProps) {
  const pageIndex = Number((await props.params).pageIndex);
  const { data, pagination } = await getArticles({ pageIndex });
  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>;
  return (
    <>
      {data?.map((article) => <ArticleCard key={article.id} article={article} pageIndex={pageIndex} />)}
      <Pagination url="/media-experience/articles" {...pagination} />
    </>
  );
}

// ==================================================================================
// مكون بطاقة المقالة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleCard({ article, pageIndex }: { article: Article; pageIndex: number }) {
  return (
    <div className="w-full space-y-5 overflow-clip rounded-md bg-white text-secondary shadow-[0_0_15px_-2px_#00000040]">
      <div className="space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/media-experience/articles/${pageIndex}/${article.id}#pagination`}
          className="text-lg font-bold text-primary underline hover:text-primary/90"
        >
          {article.title}
        </Link>
        <p className="line-clamp-3 min-h-[60px] text-sm">{article.article}</p>
      </div>
      <div className="w-full rounded-sm bg-muted/10 px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" /> <FormatTime dateInput={article.createdAt} />
        </span>
      </div>
    </div>
  );
}
