import { getArticles } from "@/utils/get-data-from-db";
import { CalendarDays } from "lucide-react";
import { siteName } from "@/utils/siteConfig";
import { notFound } from "next/navigation";
import { Article } from "@prisma/client";
import { Metadata } from "next";

import Comments from "@/components/ui/comments/comments";
import PageDivider from "@/components/ui/page-divider";
import FormatTime from "@/components/ui/format-time";
import Link from "next/link";

type ArticlePageProps = {
  params: Promise<{ articleId: string; pageIndex: string }>;
};

const genarateData = async (props: ArticlePageProps) => {
  const params = await props.params;
  const articleId = params.articleId;
  const pageIndex = Number(params.pageIndex);

  if (!pageIndex || !articleId) return notFound();
  const { data: response } = await getArticles({ pageIndex });
  const data = response?.find((article) => article.id === articleId);
  const related = response?.filter((article) => article.id !== articleId);
  if (!data) return notFound();
  return { data, related, pageIndex };
};

export async function generateMetadata(props: ArticlePageProps): Promise<Metadata> {
  const { data, pageIndex } = await genarateData(props);

  return {
    title: data.title,
    description: data.seoDescription,
    keywords: data.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      siteName: siteName,
      description: data.seoDescription,
      title: `${data.title} | د. ناهد باشطح`,
      url: `/media-experience/articles/${pageIndex}/${data.id}`,
    },
  };
}

// ==================================================================================
// صفحة المقاله
// ==================================================================================
export default async function ArticlePage(props: ArticlePageProps) {
  const { data, related, pageIndex } = await genarateData(props);

  return (
    <PageDivider
      pageContents={
        <div className="flex-1 space-y-5">
          <h1 className="text-2xl font-bold text-primary md:text-3xl">{data.title}</h1>
          <p className="whitespace-pre-line">{data.article}</p>
        </div>
      }
      titleMenu="مقالات ذات صلة"
      menuItems={
        <div className="snap-y space-y-4">
          {related?.map((article) => (
            <ArticleCard key={article.id} article={article} pageIndex={pageIndex} />
          ))}
        </div>
      }
      comments={
        <Comments
          entity="article"
          entityId={data.id}
          pathRevalidate={`/media-experience/articles/${pageIndex}/${data.id}`}
        />
      }
    />
  );
}

// ==================================================================================
// مكون بطاقة المقالة في قائمة المحتوى ذات صلة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleCard({ article, pageIndex }: { article: Article; pageIndex: number }) {
  return (
    <div className="w-full snap-start space-y-5 overflow-clip rounded-md bg-white text-secondary shadow-[0_0_15px_-2px_#00000040]">
      <div className="space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/media-experience/articles/${pageIndex}/${article.id}`}
          className="text-base font-bold text-primary underline hover:text-primary/90"
        >
          {article.title}
        </Link>
        <p className="line-clamp-3 text-sm">{article.article}</p>
      </div>
      <div className="w-full rounded-sm bg-muted/10 px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-nowrap text-xs">
          <CalendarDays className="size-4" /> <FormatTime dateInput={article.createdAt} />
        </span>
      </div>
    </div>
  );
}
