import TitleSection from "@/components/ui/title-section";
import FormatTime from "@/components/ui/format-time";
import Image from "next/image";
import Link from "next/link";

import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers";
import { Pagination } from "@/components/ui/pagination";
import { getBlogPosts } from "@/utils/get-data-from-db";
import { CalendarDays } from "lucide-react";
import { siteName } from "@/utils/siteConfig";
import { BlogPost } from "@prisma/client";
import { notFound } from "next/navigation";
import { Metadata } from "next";

type BlogPageProps = { params: Promise<{ pageIndex: string }> };

export const dynamic = "force-static";

export async function generateMetadata(props: BlogPageProps): Promise<Metadata> {
  const pageIndex = Number((await props.params).pageIndex);

  const { data, pagination } = await getBlogPosts({ pageIndex });
  const { currentPage, totalPages } = pagination;
  const titlePosts = data.flatMap((post) => post.title);

  // العلامات الوصفية للترقيم الصفحي
  const isPrevious = currentPage > 1;
  const isNext = totalPages > currentPage;
  const next = isNext ? `/blog/${currentPage + 1}` : undefined;
  const previous = isPrevious ? `/blog/${currentPage - 1}` : undefined;

  return {
    title: `المدونة | ${siteName} [${pageIndex}]`,
    description: titlePosts.join(" || "),
    pagination: { previous, next },
    keywords: titlePosts,
  };
}
export default async function BlogPage(props: BlogPageProps) {
  const pageIndex = Number((await props.params).pageIndex);
  if (!pageIndex) return notFound();

  const { data, pagination } = await getBlogPosts({ pageIndex });
  const lastPost = data?.[0];

  return (
    <div className="pt-16 min-h-svh">
      <HeaderPages />
      <div className="scroll-mt-10 px-4" id="pagination">
        {pageIndex === 1 && (
          <div>
            <Divider label="احدث منشور" />
            <LastPostCard post={lastPost} pageIndex={pageIndex} />
          </div>
        )}
        <Divider label="منشورات سابقة" />
        <div className="mb-6 grid grid-cols-1 place-content-center gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
          {data?.map((post, index) => {
            if (!index && pageIndex === 1) return null;
            return <PostCard key={post.id} post={post} pageIndex={pageIndex} />;
          })}
        </div>
        <div className="mb-28">
          <Pagination url="/blog" {...pagination} />
        </div>
      </div>
    </div>
  );
}

function HeaderPages() {
  return (
    <div className="relative bg-secondary py-32">
      <DividerRedTop />
      <TitleSection title={<h1>المـــدونـــة</h1>} classTitle="text-background" />
      <DividerRedBottom />
    </div>
  );
}

function Divider({ label }: { label: string }) {
  return (
    <div className="mb-3 mt-16 flex w-full flex-nowrap items-center gap-4 px-2">
      <h2 id="lectures" className="scroll-mt-20">
        {label}
      </h2>
      <hr className="mt-1.5 flex-1" />
    </div>
  );
}

function LastPostCard({ post, pageIndex }: { post: BlogPost; pageIndex: number }) {
  return (
    <div className="grid grid-cols-1 gap-3 rounded-xl border border-muted/5 bg-white p-2 md:grid-cols-2">
      <Link
        href={`/blog/${pageIndex}/${post.id}`}
        className="group w-full space-y-3 underline"
      >
        <div className="relative aspect-640/426 w-full overflow-clip rounded-b-sm rounded-t-lg bg-muted md:rounded-l-sm md:rounded-r-lg">
          <Image
            src={post.image}
            className="scale-105 transition-all duration-300 ease-out group-hover:scale-110"
            sizes="(max-width: 640px) 90vw, 50vw"
            alt={post.title}
            fill
          />
        </div>
        <p className="text-xl font-semibold md:hidden">{post.title}</p>
      </Link>
      <div className="md:px-6 md:pt-3">
        <Link
          href={`/blog/${pageIndex}/${post.id}`}
          className="mb-3 hidden text-3xl font-semibold underline md:mb-6 md:flex"
        >
          {post.title}
        </Link>
        <p className="mb-3 line-clamp-2 text-muted md:mb-6 md:line-clamp-4 md:text-balance">
          {post.content}
        </p>
        <p className="flex flex-nowrap items-center gap-1 text-sm text-muted md:text-base">
          <CalendarDays className="mb-1 size-4 md:size-5" />{" "}
          <FormatTime dateInput={post.createdAt} />
        </p>
      </div>
    </div>
  );
}

function PostCard({ post, pageIndex }: { post: BlogPost; pageIndex: number }) {
  return (
    <div className="space-y-3 rounded-xl border border-muted/5 bg-white p-2">
      <Link href={`/blog/${pageIndex}/${post.id}`} className="group space-y-3 underline">
        <div className="relative aspect-640/426 w-full overflow-clip rounded-b-sm rounded-t-lg bg-muted">
          <Image
            src={post.image}
            className="scale-105 transition-all duration-300 ease-out group-hover:scale-110"
            sizes="(max-width: 640px) 90vw, 50vw"
            alt={post.title}
            fill
          />
        </div>
        <p className="text-xl font-semibold">{post.title}</p>
      </Link>
      <p className="line-clamp-2 text-muted">{post.content}</p>
      <p className="flex flex-nowrap items-center gap-1 text-sm text-muted">
        <CalendarDays className="mb-1 size-4" /> <FormatTime dateInput={post.createdAt} />
      </p>
    </div>
  );
}
